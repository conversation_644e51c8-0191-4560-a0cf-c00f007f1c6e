/*=============== GOOGLE FONTS ===============*/
/* @import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap"); */

/*=============== ALIBABA FONTS ===============*/
@font-face {
  font-family: 'webfont';
  font-display: swap;
  src: url('//at.alicdn.com/t/webfont_y4ohnamert.eot'); /* IE9*/
  src: url('//at.alicdn.com/t/webfont_y4ohnamert.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('//at.alicdn.com/t/webfont_y4ohnamert.woff2') format('woff2'),
  url('//at.alicdn.com/t/webfont_y4ohnamert.woff') format('woff'), /* chrome、firefox */
  url('//at.alicdn.com/t/webfont_y4ohnamert.ttf') format('truetype'), /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
  url('//at.alicdn.com/t/webfont_y4ohnamert.svg#Alibaba-PuHuiTi-Regular') format('svg'); /* iOS 4.1- */
}

/*=============== VARIABLES CSS ===============*/
:root {
  --header-height: 3.5rem;

  /*========== Colors ==========*/
  /*Color mode HSL(hue, saturation, lightness)*/
  /*
        Purple: hsl(250, 66%, 75%)
        Blue: hsl(207, 90%, 72%)
        Pink: hsl(356, 66%, 75%)
        Teal: hsl(174, 63%, 62%)
  */
  --first-hue: 207;
  --sat: 90%;
  --lig: 72%;
  --second-hue: 219;
  --first-color: hsl(var(--first-hue), var(--sat), var(--lig));
  --first-color-alt: hsl(var(--first-hue), var(--sat), 68%); /* -4% */
  --title-color: #000000; /* 黑色字体 */
  --text-color: #000000; /* 黑色字体 */
  --text-color-light: #333333; /* 深灰色字体 */
  --body-color: #ffffff; /* 白色背景 */
  --container-color: #fbf3c4; /* 展开框颜色 */

  /*========== Font and typography ==========*/
  /*.5rem = 8px | 1rem = 16px ...*/
  --body-font: 'webfont', sans-serif;
  --biggest-font-size: 1.75rem;
  --h1-font-size: 1.5rem;
  --h2-font-size: 1.25rem;
  --h3-font-size: 1rem;
  --normal-font-size: .938rem;
  --small-font-size: .813rem;
  --smaller-font-size: .75rem;
  --tiny-font-size: .625rem;

  /*========== Font weight ==========*/
  --font-medium: 500;
  --font-semibold: 600;

  /*========== z index ==========*/
  --z-tooltip: 10;
  --z-fixed: 100;
  --z-modal: 1000;
}

/* Responsive typography */
@media screen and (min-width: 968px) {
  :root {
    --biggest-font-size: 2.5rem;
    --h1-font-size: 2.25rem;
    --h2-font-size: 1.5rem;
    --h3-font-size: 1.25rem;
    --normal-font-size: 1rem;
    --small-font-size: .875rem;
    --smaller-font-size: .813rem;
  }
}

/*=============== BASE ===============*/
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body,
button,
input,
textarea {
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
}

body {
  background-color: var(--body-color);
  color: var(--text-color);
  transition: .4s; /* for light mode animation */
}

h1, h2, h3 {
  color: var(--title-color);
  font-weight: var(--font-semibold);
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
}

img {
  max-width: 100%;
  height: auto;
}

/*=============== THEME REMOVED ===============*/


/*========== LIGHT THEME STYLES REMOVED ===============*/



/*=============== REUSABLE CSS CLASSES ===============*/
.container {
  max-width: 968px;
  margin-left: 1rem;
  margin-right: 1rem;
}

.grid {
  display: grid;
  gap: 1.25rem;
}

.main {
  overflow: hidden;
  padding-bottom: 12rem; /* 为底部导航栏和footer留出空间 */
}

.section {
  padding: 1rem 0 1rem; /* 减少上边距，因为移除了header */
}

.section__title, 
.section__subtitle {
  text-align: center;
}

.section__title {
  font-size: var(--h2-font-size);
  color: var(--first-color);
  margin-bottom: 2rem;
}

.section__subtitle {
  display: block;
  font-size: var(--smaller-font-size);
  color: var(--text-color-light);
}

/*=============== HEADER & NAV===============*/
.header {
  position: fixed;
  top: 0;
  left: 0;
  width:100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: var(--z-fixed);
  transition: .4s; /* for light mode animation */
}

.nav{
  height:var(--header-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__logo{
  color: var(--first-color);
  font-weight: var(--font-medium);
  transition: .4s;
  font-size: 22px;
}

.nav__logo:hover{
  color: var(--first-color-alt);
}

.nav__menu{
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fbf3c4; /* 设置底部导航背景颜色 */
  width: 100%;
  padding:1rem 2.25rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  transition: .4s; /* for light mode animation */
  z-index: var(--z-fixed);
}

.nav__list{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__link{
  color: var(--text-color);
  font-size: 1.25rem;
  padding: .4rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 5rem;
  text-decoration: none;
}

.nav__text{
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: var(--font-medium);
}


/* Active link */
.active-link{
  background:linear-gradient(180deg,
  hsla(var(--first-hue),var(--sat),var(--lig), 1),
  hsla(var(--first-hue),var(--sat),var(--lig), .2)
  );
  box-shadow: 0 0 16px hsla(var(--first-hue),var(--sat),var(--lig), .4);
  color: var(--title-color)
}

/* Change background header */
.scroll-header{
  box-shadow: 0 4px 4px hsla(0, 0%, 4%, .3);
}

/*=============== CONTACT ===============*/
.contact__container{
  row-gap: 3rem;
}

.contact__content{
  text-align: center;
}

.contact__info{
  display: grid;
  row-gap: 2rem;
  margin-top: 2rem;
}

.contact__item{
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 1rem;
  padding: 1rem;
  background-color: var(--container-color);
  border-radius: 1rem;
  transition: .3s;
}

.contact__item:hover{
  background-color: var(--first-color);
  color: var(--title-color);
}

.contact__icon{
  font-size: 2rem;
  color: var(--first-color);
}

.contact__item:hover .contact__icon{
  color: var(--title-color);
}

.contact__title{
  font-size: var(--h3-font-size);
  font-weight: var(--font-medium);
  margin-bottom: .25rem;
}

.contact__subtitle{
  font-size: var(--small-font-size);
  color: var(--text-color);
}

/*=============== HOME ===============*/
.home__container{
  position: relative;
  row-gap: 4.5rem;
  margin-left: 0;
  margin-right: 0;
}

.home__data{
  text-align: center;
}

.home__education,
.home__greeting{
  font-size: var(--small-font-size);
  font-weight: var(--font-medium);
}

.home__greeting{
  display: block;
  color: var(--title-color);
  margin-bottom: .5rem;
}

.home__education{
  color: var(--text-color);
  margin-top: .5rem;
  margin-bottom: 1.5rem;
}

.home__name{
  font-size: var(--biggest-font-size);
}

.home__img{
  widows: 160px;
}

/* 轮播图样式 */
.home__carousel {
  width: 100vw;
  height: 300px;
  position: relative;
  margin: 0;
  padding: 0;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

.carousel-container {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 8px 24px hsla(var(--first-hue), 48%, 8%, 0.15);
}

.carousel-slides {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.carousel-img.active {
  opacity: 1;
}

.carousel-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--h3-font-size);
  color: var(--text-color-light);
  background-color: var(--container-color);
}

.carousel-dots {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
}

.dot.active {
  background-color: var(--first-color);
  border-color: var(--first-color);
  transform: scale(1.2);
  box-shadow: 0 0 12px rgba(74, 144, 226, 0.6);
}

.dot:hover {
  background-color: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
}

/* 移动端轮播图优化 */
@media screen and (max-width: 576px) {
  .home__carousel {
    width: 100%;
    height: 250px;
  }

  .carousel-dots {
    bottom: 10px;
  }

  .dot {
    width: 8px;
    height: 8px;
  }
}

.home__buttons{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
}

.home__social,
.home__scroll{
  position: absolute;
}

.home__social{
  bottom:5rem;
  left:0;
  display: grid;
  row-gap: .5rem;
}

.home__social-link{
  width:max-content;
  background-color: var(--container-color);
  color: var(--first-color);
  padding: .25rem;
  border-radius:  .25rem;
  display: flex;
  font-size: 1rem;
  transition: 0.4s;
}

.home__social-link:hover{
  background-color: var(--first-color);
  color:aliceblue;
}

.home__social::after{
  content:'';
  width: 32px;
  height: 2px;
  background-color: var(--first-color);
  transform: rotate(90deg) translate(16px,3px);
}

.home__scroll{
  color: var(--first-color);
  right: -1rem;
  bottom: 4rem;
  display: grid;
  row-gap: .25rem;
  justify-items: center;
}

.home__scroll-icon{
  font-size: 1.25rem;
}

.home__scroll-name{
  font-size: var(--smaller-font-size);
  writing-mode:vertical-lr;
  /* transform: rotate(90deg); */
}


/*=============== BUTTONS ===============*/
.button{
  display: inline-block;
  background-color: var(--first-color);
  color: var(--body-color);
  padding: .5rem .75rem;
  border-radius:.5rem;
  font-weight: var(--font-semibold);
  transition: .4s;
}

.button:hover{
  background-color: var(--first-color-alt);
  color: var(--body-color);
}

.button--ghost{
  background-color: transparent;
  border: 2px solid var(--first-color);
  color: var(--first-color);
}
/*=============== ABOUT ===============*/
.about__container{
  row-gap: 2.5rem;
  padding-bottom: 2rem; /* 防止被底部导航栏遮挡 */
}

.about__container .about__expandable {
  margin-left: 0;
  margin-right: 0;
  padding-left: 0;
  padding-right: 0;
}
.about__img{
  width: 220px;
  border-radius:1.5rem;
  justify-self: center;
}

.about__data{
  text-align: center;
}

.about__info{
  display: grid;
  grid-template-columns:repeat(3, 1fr);;
  gap: .5rem;
  margin-bottom: 2rem;
}

.about__box{
  background-color: var(--container-color);
  border-radius: .75em;
  padding: .75rem .5rem;
}

.about__icon{
  font-size: 1.5rem;
  color: var(--first-color);
  margin-bottom: .5rem;
}

.about__title{
  font-size: var(--small-font-size);
}

.about__subtitle{
  font-size: var(--tiny-font-size);
  white-space:pre;
}

.about__description{
  margin-bottom: 2rem;
}

/*=============== EXPANDABLE LIST ===============*/
.about__expandable {
  width: 100%;
  margin: 0;
}

.expandable__item {
  background-color: var(--container-color);
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  margin-left: 0;
  margin-right: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.expandable__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
}

.expandable__header:hover {
  background-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.expandable__title {
  font-size: var(--h3-font-size);
  color: var(--title-color);
  font-weight: var(--font-semibold);
  margin: 0;
}

.expandable__icon {
  font-size: 1.5rem;
  color: var(--first-color);
  transition: transform 0.3s ease;
}

.expandable__content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.expandable__body {
  padding: 1rem  1.5rem;
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: 2rem; /* 防止被底部导航栏遮挡 */
}

/* Active state */
.expandable__item.active .expandable__icon {
  transform: rotate(90deg);
}

.expandable__item.active .expandable__content {
  max-height: 80vh; /* 使用视口高度，确保内容可见 */
  overflow-y: auto; /* 允许垂直滚动 */
}

/* 生产追溯容器滚动优化 */
.production-trace-container {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 5px; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
.expandable__content::-webkit-scrollbar,
.production-trace-container::-webkit-scrollbar {
  width: 6px;
}

.expandable__content::-webkit-scrollbar-track,
.production-trace-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.expandable__content::-webkit-scrollbar-thumb,
.production-trace-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.expandable__content::-webkit-scrollbar-thumb:hover,
.production-trace-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/*=============== PRODUCT INFO FORM ===============*/
.product-info-form {
  width: 100%;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.form-label {
  min-width: 80px;
  font-size: var(--normal-font-size);
  color: rgba(255, 255, 255, 0.9);
  font-weight: var(--font-medium);
  text-align: left;
}

/* 标签颜色 */
.form-label {
  color: var(--title-color);
}

.form-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background-color: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 0.75rem;
  color: rgba(255, 255, 255, 0.85);
  font-size: var(--normal-font-size);
  font-family: var(--body-font);
  cursor: default;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-input:focus {
  outline: none;
  border-color: var(--first-color);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
  transform: translateY(-1px);
}

.form-input[readonly] {
  background-color: rgba(255, 255, 255, 0.06);
  cursor: default;
  border-color: rgba(255, 255, 255, 0.12);
}

/* 表单输入框样式 */
.form-input {
  background-color: #f8f9fa;
  border-color: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

.form-input[readonly] {
  background-color: #f1f3f4;
}

/* 可展开内容样式 */
.expandable__content {
  background-color: var(--container-color);
}

.expandable__body {
  background-color: var(--container-color);
  border-radius: 0.5rem;
  padding: 1rem;
}

/* 生产追溯容器样式 */
.production-trace-container {
  background-color: var(--container-color);
}

.trace-info-card {
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

/* 可展开项样式优化 */
.expandable__header {
  background-color: var(--container-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.expandable__header:hover {
  background-color: var(--container-color);
  opacity: 0.9;
}









/*=============== FOOTER ===============*/
.footer{
  position: fixed;
  bottom: 5rem; /* 在导航菜单上方 */
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  background-color: #fbf3c4; /* 设置底部背景颜色 */
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  z-index: calc(var(--z-fixed) - 1);
}

.footer__container{
  padding: 0.75rem 1rem; /* 缩小高度 */
  background-color: transparent; /* 移除背景色，使用父元素的背景 */
  max-width: none;
  margin-left: 0;
  margin-right: 0;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

/* Footer Image Section */
.footer__image-section{
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  max-width: 968px;
  margin-left: auto;
  margin-right: auto;
  gap: 1rem;
  max-height: 80px;
  overflow: hidden;
}

.footer__logo-area{
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center;
  height: auto; /* 改为自动高度 */
  text-align: center; /* 文字居中 */
}

.footer__logo-text{
  font-size: var(--normal-font-size); /* 缩小字体 */
  font-weight: var(--font-semi-bold);
  color: #000000; /* 黑色字体 */
  margin-bottom: 0.125rem; /* 减小间距 */
}

.footer__logo-subtitle{
  font-size: var(--small-font-size); /* 稍微调整字体大小 */
  color: #333333; /* 深灰色字体 */
  font-style: italic;
}

.footer__image-info{
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: var(--smaller-font-size);
  color: var(--text-color);
  text-align: right;
}

.footer__visitors{
  font-weight: var(--font-medium);
  color: var(--first-color);
}

.footer__platform{
  font-weight: var(--font-medium);
  color: var(--title-color);
}

.footer__icp{
  color: var(--text-color-light);
  font-size: var(--tiny-font-size);
}

/* Footer Content */
.footer__content{
  text-align: center;
}

.footer__title,
.footer__link{
  color: var(--body-color);
}

.footer__title{
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: var(--h3-font-size);
}

.footer__list{
  display: flex;
  justify-content: center;
  column-gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.footer__social{
  display: flex;
  justify-content: center;
  column-gap: 1.25rem;
}

.footer__social-link{
  background-color: var(--body-color);
  color: var(--first-color);
  padding: 0.25rem;
  border-radius:.25rem;
  font-size: 1rem;
  display: inline-flex;
}

.footer__copy{
  display: block;
  margin-top: 1.5rem;
  color: var(--container-color);
  text-align:center;
  font-size: var(--smaller-font-size);
}
/*=============== SCROLL BAR ===============*/
::-webkit-scrollbar{
  width: 0.6rem;
  border-radius:.5rem;
  background-color: hsl(var(--second-hue), 8%, 38%);
}

::-webkit-scrollbar-thumb{
  background-color: hsl(var(--second-hue), 8%, 26%);
  border-radius:.5rem;
}

::-webkit-scrollbar-thumb:hover{
  background-color: hsl(var(--second-hue), 8%, 20%);
}


/*=============== BREAKPOINTS ===============*/
/* For small devices */
@media screen and (max-width:320px){
  .nav__menu{
    padding:1rem 1.5rem;
  }

  .home__buttons{
    flex-direction: column;
  }


  .home__carousel{
    width: 100%;
    height: 200px;
  }

  .about__info{
    grid-template-columns: repeat(2,1fr)
  }




}

/* For medium devices */
@media screen and (min-width:576px) {
  .nav__menu{
    width: 100%;
  }

  .about__info{
    grid-template-columns: repeat(3,140px);
    justify-content: center;
  }

  .about__description{
    padding:0 5rem;
  }




}

@media screen and (min-width: 767px) {
  /* 中等屏幕设备的样式可以在这里添加 */
}

/* For large devices */
@media screen and (min-width:992px){
  .container{
    margin-left: auto;
    margin-right: auto;
  }

  .section{
    padding:2rem 0 1rem; /* 减少上边距，因为移除了header */
  }

  .section__title{
    margin-bottom: 3.5rem;
  }

  .nav{
    height:calc(var(--header-height) + 1rem)
  }

  .home__carousel{
    width: 100vw;
    height: 350px;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
  }
  .home__social-link{
    padding:.4rem;
    font-size: 1.25rem;
  }

  .home__social::after{
    transform:rotate(90deg) translate(16px,0)
  }

  .home__scroll-icon{
    font-size: 2rem;
  }





   .footer__social-link{
     font-size: 1.25rem;
     padding: .4rem;
     border-radius:.5rem;
   }

   /* Footer responsive */
   .footer__image-section{
     flex-direction: column;
     text-align: center;
     max-height: 120px;
   }

   .footer__logo-area{
     align-items: center;
   }

   .footer__logo-text{
     font-size: var(--normal-font-size);
   }

   .footer__image-info{
     text-align: center;
     font-size: var(--tiny-font-size);
   }
}

/*=============== PRODUCTION TRACE ===============*/
.production-trace-container {
  width: 100%;
}

.trace-info-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.06));
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.trace-info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}



/* 保留原有的样式以防其他地方使用 */
.trace-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.trace-info-row:last-child {
  border-bottom: none;
}

.trace-label {
  font-weight: var(--font-semibold);
  color: var(--title-color);
  font-size: var(--normal-font-size);
}

.trace-value {
  color: var(--first-color);
  font-weight: var(--font-medium);
  font-size: var(--normal-font-size);
}

/* Timeline Styles */
.timeline-container {
  position: relative;
  padding-left: 3rem;
  margin-top: 2rem;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, var(--first-color), rgba(255, 255, 255, 0.3));
  border-radius: 2px;
  z-index: 1;
}

.timeline-item {
  position: relative;
  margin-bottom: 2.5rem;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
}

.timeline-item:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.25);
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -2.25rem;
  top: 1.5rem;
  width: 12px;
  height: 12px;
  background-color: var(--body-color);
  border: 3px solid var(--first-color);
  border-radius: 50%;
  z-index: 2;
  box-shadow: 0 0 0 4px var(--body-color);
}

.timeline-item::after {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 1.5rem;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 12px solid var(--container-color);
  z-index: 1;
}

.timeline-date {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.08));
  padding: 1.25rem 1.5rem 1rem;
  color: var(--text-color-light);
  font-size: 0.875rem;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  backdrop-filter: blur(10px);
}

.timeline-date span {
  color: var(--title-color);
  font-weight: 700;
  font-size: 1rem;
}

.timeline-content {
  padding: 1.5rem;
  background-color: var(--container-color);
}

.timeline-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  /* flex-direction: column; */
  align-items: flex-start;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 0.5rem;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  line-height: 1.4;
}

.detail-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  line-height: 1.4;
  text-align: left;
  word-break: break-word;
  padding-left: 1rem;
}

/* 时间线样式 */
.timeline-container::before {
  background-color: #dee2e6;
}

.timeline-item {
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.timeline-item::before {
  background-color: #ffffff;
  border-color: #4a90e2;
  box-shadow: 0 0 0 4px #ffffff;
}

.timeline-item::after {
  border-right-color: #ffffff;
}

.timeline-date {
  background-color: #f8f9fa;
  color: #6c757d;
  border-bottom-color: #e9ecef;
}

.timeline-date span {
  color: #495057;
}

.detail-label {
  color: #6c757d;
}

.detail-value {
  color: #212529;
}

.detail-row {
  border-bottom-color: #f8f9fa;
}

/* Responsive adjustments for production trace */
@media screen and (max-width: 768px) {
  .timeline-container {
    padding-left: 2rem;
  }

  .timeline-container::before {
    left: 1rem;
  }

  .timeline-item::before {
    left: -1.75rem;
  }

  .timeline-item::after {
    left: -1rem;
  }

  .timeline-date {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .detail-row {
    /* flex-direction: column; */
    gap: 0.25rem;
    align-items: flex-start;
  }

  .detail-label {
    min-width: auto;
    margin-bottom: 0.25rem;
  }

  .detail-value {
    text-align: left;
    max-width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .timeline-container {
    padding-left: 1.5rem;
  }

  .timeline-container::before {
    left: 0.75rem;
  }

  .timeline-item::before {
    left: -1.5rem;
  }

  .timeline-item::after {
    left: -0.75rem;
  }

  .timeline-date {
    padding: 1rem;
  }

  .timeline-content {
    padding: 1rem;
  }
}
