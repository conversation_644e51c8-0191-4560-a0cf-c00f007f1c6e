<!DOCTYPE html>
<html lang="zh-Hans">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!--=============== FAVICON ===============-->
    <link rel="shortcut icon" href="assets/img/favicon.png" type="image/x-icon">

    <!--=============== BOXICONS ===============-->
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>

    <!--=============== SWIPER CSS ===============-->
    <link rel="stylesheet" href="assets/css/swiper-bundle.min.css">

    <!--=============== CSS ===============-->
    <link rel="stylesheet" href="assets/css/styles.css">

    <!--=============== TIMELINE CSS ===============-->
    <link rel="stylesheet" href="assets/css/timeline.css">

    <!--=============== IMAGE MODAL CSS ===============-->
    <style>
        /* 图片弹窗样式 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .image-modal-content {
            position: relative;
            margin: auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .modal-image {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .image-modal-close {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #fff;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10000;
            transition: color 0.3s ease;
        }

        .image-modal-close:hover,
        .image-modal-close:focus {
            color: #ccc;
        }

        .image-modal-caption {
            color: #fff;
            text-align: center;
            margin-top: 15px;
            font-size: 16px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            padding: 10px 20px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 页面背景样式 */
        body {
            background-color: #ffffff !important;
        }

        /* 强制修复导航菜单位置 - 适用于所有屏幕尺寸 */
        .nav__menu {
            position: fixed !important;
            bottom: 0 !important;
            top: auto !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            z-index: 9999 !important;
            transform: none !important;
            display: flex !important;
            justify-content: center !important;
        }

        /* 导航菜单样式 */
        .nav__menu {
            background-color: #fbf3c4 !important;
            border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
        }

        /* 在所有屏幕尺寸下强制底部导航 */
        @media screen and (min-width: 768px) {
            .nav__menu {
                position: fixed !important;
                bottom: 0 !important;
                top: auto !important;
            }
        }

        @media screen and (min-width: 992px) {
            .nav__menu {
                position: fixed !important;
                bottom: 0 !important;
                top: auto !important;
            }
        }

        /* 确保header在顶部 */
        .header {
            position: fixed !important;
            top: 0 !important;
            bottom: auto !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            z-index: 9998 !important;
        }

        /* HEADER STYLES REMOVED */

        /* 为主要内容区域添加微妙的光晕效果 */
        .home__carousel {
            position: relative;
        }

        .home__carousel::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 20px;
            z-index: -1;
            pointer-events: none;
        }

        /* 增强滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
            border-radius: 4px;
            backdrop-filter: blur(10px);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.3));
        }

        /* 调试样式 - 确保布局正确 */
        .nav__menu {
            padding: 1rem 2.25rem !important;
        }

        /* 修复导航菜单布局 */
        .nav__list {
            display: flex !important;
            justify-content: space-around !important;
            align-items: center !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .nav__item {
            flex: 1 !important;
            display: flex !important;
            justify-content: center !important;
        }

        .nav__link {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            padding: 0.5rem !important;
            text-decoration: none !important;
            color: var(--text-color) !important;
            border-radius: 0.5rem !important;
            transition: all 0.3s ease !important;
            min-width: 60px !important;
        }

        .nav__link i {
            font-size: 1.5rem !important;
            margin-bottom: 0.25rem !important;
        }

        .nav__text {
            font-size: 0.75rem !important;
            font-weight: 500 !important;
            text-align: center !important;
            white-space: nowrap !important;
        }

        /* 导航链接颜色 */
        .nav__link {
            color: var(--title-color) !important;
        }

        .nav__link.active-link {
            background: linear-gradient(180deg,
                hsla(207, 90%, 72%, 1),
                hsla(207, 90%, 72%, 0.2)) !important;
            box-shadow: 0 0 16px hsla(207, 90%, 72%, 0.4) !important;
            color: #fff !important;
        }

        /* 表单样式 */
        .form-label {
            color: #000000 !important;
        }

        .form-input {
            color: #000000 !important;
            background-color: #f8f9fa !important;
            border-color: rgba(0, 0, 0, 0.1) !important;
        }

        /* 表单图片容器样式 */
        .form-input-container {
            min-height: 40px;
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        /* 时间线样式 */
        .detail-label {
            color: #6c757d !important;
        }

        .detail-value {
            color: #212529 !important;
        }

        /* 时间线日期样式 */
        .timeline-date {
            color: #6c757d !important;
        }

        .timeline-date span {
            color: #495057 !important;
            font-weight: 600;
        }

        /* 确保主内容区域有正确的边距 */
        .main {
            padding-bottom: 6rem !important; /* 为底部导航留出空间 */
        }

        /* 确保页脚不与导航冲突 */
        .footer {
            bottom: 6rem !important; /* 在导航菜单上方 */
        }

        /* 活跃链接样式 */
        .nav__link.active-link {
            background: linear-gradient(180deg,
                hsla(207, 90%, 72%, 1),
                hsla(207, 90%, 72%, 0.2)) !important;
            box-shadow: 0 0 16px hsla(207, 90%, 72%, 0.4) !important;
            color: #fff !important;
        }

        /* 底部导航按钮默认状态 - 不选中 */
        .nav__link {
            background: transparent !important;
            box-shadow: none !important;
        }

        /* 悬停效果 */
        .nav__link:hover {
            background: rgba(0, 0, 0, 0.05) !important;
            transform: translateY(-2px) !important;
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .nav__menu {
                padding: 0.75rem 1rem !important;
            }

            .nav__link {
                padding: 0.4rem !important;
                min-width: 50px !important;
            }

            .nav__link i {
                font-size: 1.25rem !important;
            }

            .nav__text {
                font-size: 0.7rem !important;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .image-modal-content {
                padding: 10px;
            }

            .image-modal-close {
                top: 10px;
                right: 15px;
                font-size: 30px;
            }

            .modal-image {
                max-height: 70vh;
            }
        }
    </style>

    <title id="page-title">加载中...</title>
</head>

<body>
    <!-- HEADER REMOVED -->

    <!--=============== BOTTOM NAVIGATION ===============-->
    <div class="nav__menu">
        <ul class="nav__list">
            <li class="nav__item">
                <a href="javascript:void(0)" class="nav__link" id="official-website-btn">
                    <i class='bx bx-home'></i>
                    <span class="nav__text">集团官网</span>
                </a>
            </li>
            <li class="nav__item">
                <a href="javascript:void(0)" class="nav__link" id="company-intro-btn">
                    <i class='bx bx-building'></i>
                    <span class="nav__text">企业简介</span>
                </a>
            </li>
            <li class="nav__item">
                <a href="javascript:void(0)" class="nav__link" id="contact-service-btn">
                    <i class='bx bx-phone'></i>
                    <span class="nav__text">联系客服</span>
                </a>
            </li>
        </ul>
    </div>

    <!--=============== MAIN ===============-->
    <main class="main">
        <!--=============== HOME ===============-->
        <section class="home section" id="home">
            <div class="home__container container grid">
                <div class="home__carousel">
                    <div class="carousel-container">
                        <div class="carousel-slides" id="carousel-slides">
                            <!-- 轮播图将通过JavaScript动态生成 -->
                            <div class="carousel-loading">加载中...</div>
                        </div>
                        <div class="carousel-dots" id="carousel-dots">
                            <!-- 圆点将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!--=============== ABOUT ===============-->
        <section class="about section" id="about">
            <div class="about__container grid">
                <div class="about__expandable" id="about__expandable">
                    <!-- 产品信息模块将通过JavaScript动态生成 -->
                    <div id="product-info-container">
                        <div class="product-info-loading">加载中...</div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="production-trace">
                            <h3 class="expandable__title">生产追溯</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="production-trace">
                            <div class="expandable__body">
                                <!-- 生产追溯内容 -->
                                <div class="production-trace-container">
                                    <!-- 基本信息卡片 -->
                                    <div class="trace-info-card">
                                        <div class="form-row">
                                            <label class="form-label">生产批号</label>
                                            <input type="text" class="form-input" id="production-batch-no" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">生产日期</label>
                                            <input type="text" class="form-input" id="production-date" value="加载中..." readonly>
                                        </div>
                                        <div class="form-row">
                                            <label class="form-label">保质期</label>
                                            <input type="text" class="form-input" id="expiration-date" value="加载中..." readonly>
                                        </div>
                                    </div>

                                    <!-- 时间线 -->
                                    <div class="timeline-container" id="timeline-container">
                                        <!-- 时间线将通过JavaScript动态生成 -->
                                        <div class="timeline-loading">加载中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="expandable__item">
                        <div class="expandable__header" data-target="company-intro">
                            <h3 class="expandable__title">企业介绍</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                        <div class="expandable__content" id="company-intro">
                            <div class="expandable__body">
                                <!-- 企业介绍内容将在这里显示 -->
                                <p>企业介绍详细内容...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!--=============== IMAGE MODAL ===============-->
    <div id="imageModal" class="image-modal">
        <div class="image-modal-content">
            <span class="image-modal-close">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <div class="image-modal-caption" id="imageCaption"></div>
        </div>
    </div>

    <!--=============== FOOTER ===============-->
    <!-- <footer class="footer">
        <div class="footer__container container">
            <div class="footer__image-section">
                <div class="footer__logo-area">
                    <div class="footer__logo-text">鲲鹏数字化追溯平台</div>
                   <span class="footer__icp">鲁ICP备2022003686号 ********</span>
                </div>
            </div>
         
        </div>
    </footer> -->
<!-- 
    <script src="/0001/assets/js/scrollreveal.min.js"></script>

    <script src="/0001/assets/js/swiper-bundle.min.js"></script>

    <script src="/0001/assets/js/mixitup.min.js"></script>

    <script src="/0001/assets/js/main.js"></script> -->

    <!--=============== API CALL SCRIPT ===============-->
    <script>
        // =============== API配置 ===============
        const API_CONFIG = {
            // 开发环境 - 使用本地代理服务器
            development: 'http://localhost:3001/api',
            // 生产环境 - 直接调用昆鹏360接口
            production: 'https://www.kunpeng360.com/CustomerQuery/Query?t=@t@&barcode=@barcode@'
        };

        // 自动检测环境
        const API_BASE_URL = window.location.hostname === 'localhost' ||
                             window.location.hostname === '127.0.0.1' ||
                             window.location.hostname === ''
            ? API_CONFIG.development
            : API_CONFIG.production;

        // 环境检测函数
        function isProductionEnvironment() {
            return window.location.hostname !== 'localhost' &&
                   window.location.hostname !== '127.0.0.1' &&
                   window.location.hostname !== '';
        }

        console.log('当前环境检测:');
        console.log('hostname:', window.location.hostname);
        console.log('使用API地址:', API_BASE_URL);

        // 显示当前环境的数据处理方式
        if (isProductionEnvironment()) {
            console.log('🌐 当前环境: 生产环境');
            console.log('📊 数据处理: 直接使用API响应 (result)');
            console.log('🔗 API调用: 直接调用昆鹏360接口');
        } else {
            console.log('🛠️ 当前环境: 开发环境');
            console.log('📊 数据处理: 使用代理响应数据 (result.data)');
            console.log('🔗 API调用: 通过代理服务器');
        }

        // 更新页面标题的函数
        function updatePageTitle(apiData) {
            try {
                const entName = apiData.EntName || '未知企业';

                // 更新页面标题
                const pageTitleElement = document.getElementById('page-title');
                if (pageTitleElement) {
                    pageTitleElement.textContent = entName;
                }

                // 更新导航栏logo
                const navLogoElement = document.getElementById('nav-logo');
                if (navLogoElement) {
                    navLogoElement.textContent = entName;
                }

                console.log('✅ 页面标题已更新:', entName);

            } catch (error) {
                console.error('更新页面标题时发生错误:', error);
                const pageTitleElement = document.getElementById('page-title');
                const navLogoElement = document.getElementById('nav-logo');
                if (pageTitleElement) pageTitleElement.textContent = '加载失败';
                if (navLogoElement) navLogoElement.textContent = '加载失败';
            }
        }

        // 更新轮播图的函数
        function updateCarousel(apiData) {
            try {
                const carouselImages = apiData.CarouselImages || [];
                const entCode = apiData.EntCode || '@t@';

                console.log('轮播图数据:', carouselImages);
                console.log('企业代码:', entCode);

                const carouselSlidesContainer = document.getElementById('carousel-slides');
                const carouselDotsContainer = document.getElementById('carousel-dots');

                if (!carouselSlidesContainer || !carouselDotsContainer) {
                    console.error('找不到轮播图容器元素');
                    return;
                }

                // 清空现有内容
                carouselSlidesContainer.innerHTML = '';
                carouselDotsContainer.innerHTML = '';

                if (carouselImages.length === 0) {
                    carouselSlidesContainer.innerHTML = '<div class="carousel-loading">暂无轮播图</div>';
                    return;
                }

                // 生成轮播图片
                carouselImages.forEach((imageId, index) => {
                    const imageUrl = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${imageId}`;

                    const imgElement = document.createElement('img');
                    imgElement.src = imageUrl;
                    imgElement.alt = `轮播图${index + 1}`;
                    imgElement.className = 'carousel-img';
                    imgElement.style.cursor = 'pointer';
                    if (index === 0) {
                        imgElement.classList.add('active');
                    }

                    // 添加点击事件显示弹窗
                    imgElement.onclick = function() {
                        showImageModal(this.src, `轮播图${index + 1}`);
                    };

                    carouselSlidesContainer.appendChild(imgElement);

                    // 生成对应的圆点
                    const dotElement = document.createElement('span');
                    dotElement.className = 'dot';
                    dotElement.setAttribute('data-slide', index.toString());
                    if (index === 0) {
                        dotElement.classList.add('active');
                    }

                    carouselDotsContainer.appendChild(dotElement);
                });

                // 重新初始化轮播功能
                initializeCarousel();

                console.log('✅ 轮播图已更新，共', carouselImages.length, '张图片');

            } catch (error) {
                console.error('更新轮播图时发生错误:', error);
                const carouselSlidesContainer = document.getElementById('carousel-slides');
                if (carouselSlidesContainer) {
                    carouselSlidesContainer.innerHTML = '<div class="carousel-loading">轮播图加载失败</div>';
                }
            }
        }

        // 初始化轮播功能
        function initializeCarousel() {
            let currentSlide = 0;
            const slides = document.querySelectorAll('.carousel-img');
            const dots = document.querySelectorAll('.dot');
            const totalSlides = slides.length;

            if (totalSlides === 0) return;

            function showSlide(index) {
                // 移除所有active类
                slides.forEach(slide => slide.classList.remove('active'));
                dots.forEach(dot => dot.classList.remove('active'));

                // 添加active类到当前幻灯片和点
                if (slides[index]) slides[index].classList.add('active');
                if (dots[index]) dots[index].classList.add('active');
            }

            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }

            // 点击圆点切换图片
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentSlide = index;
                    showSlide(currentSlide);
                });
            });

            // 清除之前的定时器（如果存在）
            if (window.carouselInterval) {
                clearInterval(window.carouselInterval);
            }

            // 自动轮播，每3秒切换一次
            window.carouselInterval = setInterval(nextSlide, 3000);
        }

        // 更新页面产品信息的函数
        function updateProductInfo(apiData) {
            try {
                // 获取产品模板信息
                const productTemplate = apiData.ProductTemplate || {};
                const propertySessions = productTemplate.PropertySessions || [];

                console.log('ProductTemplate:', productTemplate);
                console.log('PropertySessions:', propertySessions);

                // 获取产品信息容器
                const productInfoContainer = document.getElementById('product-info-container');
                if (!productInfoContainer) {
                    console.error('找不到产品信息容器');
                    return;
                }

                // 清空现有内容
                productInfoContainer.innerHTML = '';

                if (propertySessions.length === 0) {
                    productInfoContainer.innerHTML = '<div class="product-info-loading">暂无产品信息</div>';
                    return;
                }

                // 遍历PropertySessions数组，为每个SessionName创建折叠面板
                propertySessions.forEach((session, index) => {
                    const sessionName = session.SessionName || `产品信息${index + 1}`;
                    const sessionId = `product-info-${index}`;
                    const properties = session.Properties || [];

                    console.log(`创建产品信息面板: ${sessionName}`);
                    console.log(`Properties:`, properties);

                    // 创建折叠面板HTML结构
                    const expandableItem = document.createElement('div');
                    expandableItem.className = 'expandable__item';

                    // 创建头部
                    const headerHtml = `
                        <div class="expandable__header" data-target="${sessionId}">
                            <h3 class="expandable__title">${sessionName}</h3>
                            <i class='bx bx-chevron-right expandable__icon'></i>
                        </div>
                    `;

                    // 动态生成表单内容
                    let formRowsHtml = '';
                    const entCode = apiData.EntCode || '@t@';

                    properties.forEach(property => {
                        const propertyName = property.PropertyName || '';
                        const propertyType = property.PropertyType || 0;
                        if (!propertyName) return; // 跳过空的PropertyName

                        // 构建查找键：SessionName_PropertyName
                        const lookupKey = `${sessionName}_${propertyName}`;

                        // 从ProductProperties中获取对应的值
                        const productProperties = apiData.ProductProperties || {};
                        const propertyValue = productProperties[lookupKey] || '暂无数据';

                        console.log(`查找键: ${lookupKey}, 值: ${propertyValue}, 类型: ${propertyType}`);

                        // 根据属性类型生成不同的表单行HTML
                        if (propertyType === 7 && propertyValue !== '暂无数据') {
                            // 图片类型 - 根据环境选择图片URL
                            let imageUrl;
                            if (isProductionEnvironment()) {
                                imageUrl = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${propertyValue}`;
                            } else {
                                imageUrl = `http://localhost:3001/api/image?t=${entCode}&imageId=${propertyValue}`;
                            }

                            // 创建包含图片的表单行
                            formRowsHtml += `
                                <div class="form-row">
                                    <label class="form-label">${propertyName}</label>
                                    <div class="form-input-container">
                                        <img src="${imageUrl}"
                                             alt="${propertyName}"
                                             style="max-width: 200px; max-height: 150px; object-fit: contain; border: 1px solid #ddd; border-radius: 4px; margin-top: 5px; cursor: pointer;"
                                             onclick="showImageModal(this.src, '${propertyName}')"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                        <span style="color: #999; font-style: italic; display: none;">图片加载失败</span>
                                    </div>
                                </div>
                            `;
                        } else {
                            // 文本类型 - 创建普通的文本输入框
                            formRowsHtml += `
                                <div class="form-row">
                                    <label class="form-label">${propertyName}</label>
                                    <input type="text" class="form-input" value="${propertyValue}" readonly>
                                </div>
                            `;
                        }
                    });

                    // 如果没有Properties，显示提示信息
                    if (formRowsHtml === '') {
                        formRowsHtml = `
                            <div class="form-row">
                                <label class="form-label">提示</label>
                                <input type="text" class="form-input" value="暂无属性信息" readonly>
                            </div>
                        `;
                    }

                    // 组合完整的HTML
                    expandableItem.innerHTML = headerHtml + `
                        <div class="expandable__content" id="${sessionId}">
                            <div class="expandable__body">
                                <div class="product-info-form">
                                    ${formRowsHtml}
                                </div>
                            </div>
                        </div>
                    `;

                    productInfoContainer.appendChild(expandableItem);

                    // 为新添加的面板初始化折叠功能（手风琴效果）
                    initExpandableItemForElement(expandableItem);
                });

                console.log(`✅ 动态产品信息面板已生成，共 ${propertySessions.length} 个面板`);

            } catch (error) {
                console.error('更新产品信息时发生错误:', error);
                const productInfoContainer = document.getElementById('product-info-container');
                if (productInfoContainer) {
                    productInfoContainer.innerHTML = '<div class="product-info-loading">产品信息加载失败</div>';
                }
            }
        }

        // 更新生产追溯信息的函数
        function updateProductionTrace(apiData) {
            try {
                // 更新基本信息卡片
                const batchNoElement = document.getElementById('production-batch-no');
                const productionDateElement = document.getElementById('production-date');
                const expirationDateElement = document.getElementById('expiration-date');

                if (batchNoElement) {
                    batchNoElement.value = apiData.ProductionBatchNo || '未知批号';
                }

                if (productionDateElement) {
                    productionDateElement.value = apiData.ProductionDate || '未知日期';
                }

                if (expirationDateElement) {
                    expirationDateElement.value = apiData.ExpirationDate || '未知日期';
                }

                // 动态生成时间线
                generateDynamicTimeline(apiData);

                console.log('✅ 生产追溯信息已更新');
                console.log('生产批号:', apiData.ProductionBatchNo);
                console.log('生产日期:', apiData.ProductionDate);
                console.log('保质期:', apiData.ExpirationDate);

            } catch (error) {
                console.error('更新生产追溯信息时发生错误:', error);
                // 如果更新失败，显示错误信息
                const traceElements = ['production-batch-no', 'production-date', 'expiration-date'];
                traceElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = '加载失败';
                    }
                });
            }
        }

        // 动态生成时间线的函数
        function generateDynamicTimeline(apiData) {
            try {
                const timelineContainer = document.getElementById('timeline-container');
                if (!timelineContainer) {
                    console.error('找不到时间线容器');
                    return;
                }

                // 获取生命周期节点和数据
                const lifeCycleNodes = apiData.LifeCycleNodes || [];
                const lifeCycles = apiData.LifeCycles || {};
                const entCode = apiData.EntCode || '@t@';

                console.log('LifeCycleNodes:', lifeCycleNodes);
                console.log('LifeCycles:', lifeCycles);

                // 清空现有内容
                timelineContainer.innerHTML = '';

                if (lifeCycleNodes.length === 0) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">暂无时间线数据</div>';
                    return;
                }

                // 遍历每个生命周期节点
                lifeCycleNodes.forEach((node, index) => {
                    const nodeName = node.NodeName || `节点${index + 1}`;
                    const properties = node.Properties || [];

                    // 获取开始时间
                    const startTimeKey = `${nodeName}_开始时间`;
                    const startTime = lifeCycles[startTimeKey] || '未知时间';

                    // 创建时间线项目
                    const timelineItem = document.createElement('div');
                    timelineItem.className = 'timeline-item';
                    timelineItem.id = `timeline-${nodeName.replace(/\s+/g, '-')}`;

                    // 创建时间线日期
                    const timelineDate = document.createElement('div');
                    timelineDate.className = 'timeline-date';
                    timelineDate.innerHTML = `<span>${startTime}</span> ${nodeName}`;

                    // 创建时间线内容
                    const timelineContent = document.createElement('div');
                    timelineContent.className = 'timeline-content';

                    const timelineDetails = document.createElement('div');
                    timelineDetails.className = 'timeline-details';

                    // 遍历属性
                    properties.forEach(property => {
                        const propertyName = property.PropertyName || '';
                        const propertyType = property.PropertyType || 0;

                        if (!propertyName || propertyName === '开始时间') return; // 跳过开始时间属性

                        // 构建查找键：NodeName_PropertyName（去掉冒号）
                        const cleanPropertyName = propertyName.replace(/：$/, '');
                        const lookupKey = `${nodeName}_${cleanPropertyName}`;
                        let value = lifeCycles[lookupKey];

                        // 如果没找到，尝试带冒号的版本
                        if (!value && propertyName.endsWith('：')) {
                            const lookupKeyWithColon = `${nodeName}_${propertyName}`;
                            value = lifeCycles[lookupKeyWithColon];
                        }

                        if (!value) {
                            console.log(`未找到数据: ${lookupKey}`);
                            return; // 如果没有值，跳过
                        }

                        // 创建详情行
                        const detailRow = document.createElement('div');
                        detailRow.className = 'detail-row';

                        const detailLabel = document.createElement('span');
                        detailLabel.className = 'detail-label';
                        detailLabel.textContent = propertyName.endsWith('：') ? propertyName : `${propertyName}：`;

                        const detailValue = document.createElement('span');
                        detailValue.className = 'detail-value';

                        // 根据属性类型处理值
                        if (propertyType === 7) {
                            // 图片类型 - 根据环境选择图片URL
                            const img = document.createElement('img');

                            // 根据环境动态选择图片URL
                            if (isProductionEnvironment()) {
                                img.src = `https://www.kunpeng360.com/CustomerQuery/Image?t=${entCode}&imageId=${value}`;
                            } else {
                                img.src = `http://localhost:3001/api/image?t=${entCode}&imageId=${value}`;
                            }

                            img.alt = cleanPropertyName;
                            img.style.maxWidth = '200px';
                            img.style.maxHeight = '150px';
                            img.style.objectFit = 'contain';
                            img.style.border = '1px solid #ddd';
                            img.style.borderRadius = '4px';
                            img.style.marginTop = '5px';
                            img.style.cursor = 'pointer';

                            // 添加点击放大功能
                            img.onclick = function() {
                                showImageModal(this.src, cleanPropertyName);
                            };

                            // 添加加载错误处理
                            img.onerror = function() {
                                this.style.display = 'none';
                                const errorText = document.createElement('span');
                                errorText.textContent = '图片加载失败';
                                errorText.style.color = '#999';
                                errorText.style.fontStyle = 'italic';
                                detailValue.appendChild(errorText);
                            };

                            detailValue.appendChild(img);
                        } else {
                            // 文本类型
                            detailValue.textContent = value;
                        }

                        detailRow.appendChild(detailLabel);
                        detailRow.appendChild(detailValue);
                        timelineDetails.appendChild(detailRow);
                    });

                    // 只有当有内容时才添加时间线项目
                    if (timelineDetails.children.length > 0) {
                        timelineContent.appendChild(timelineDetails);
                        timelineItem.appendChild(timelineDate);
                        timelineItem.appendChild(timelineContent);
                        timelineContainer.appendChild(timelineItem);
                    }
                });

                console.log('✅ 动态时间线已生成，共', lifeCycleNodes.length, '个节点');

            } catch (error) {
                console.error('生成动态时间线时发生错误:', error);
                const timelineContainer = document.getElementById('timeline-container');
                if (timelineContainer) {
                    timelineContainer.innerHTML = '<div class="timeline-loading">时间线生成失败</div>';
                }
            }
        }

        // 全局变量存储API数据
        let globalApiData = null;

        // 更新企业介绍的函数
        async function updateCompanyIntro(apiData) {
            try {
                // 获取企业介绍的docId
                const entPublicize = apiData.EntPublicize || {};
                const companyIntroDocId = entPublicize['企业介绍'];
                const entCode = apiData.EntCode || '@t@';

                console.log('企业介绍docId:', companyIntroDocId);

                if (!companyIntroDocId) {
                    console.log('未找到企业介绍docId');
                    const companyIntroElement = document.querySelector('#company-intro .expandable__body');
                    if (companyIntroElement) {
                        companyIntroElement.innerHTML = '<p>暂无企业介绍信息</p>';
                    }
                    return;
                }

                let introApiUrl;
                if (isProductionEnvironment()) {
                    // 生产环境：直接调用昆鹏360接口
                    introApiUrl = `https://www.kunpeng360.com/CustomerQuery/DocHtml?t=${entCode}&docId=${companyIntroDocId}`;
                    console.log('生产环境 - 直接获取企业介绍:', introApiUrl);
                } else {
                    // 开发环境：通过代理服务器调用
                    introApiUrl = `http://localhost:3001/api/company-intro?t=${entCode}&docId=${companyIntroDocId}`;
                    console.log('开发环境 - 通过代理获取企业介绍:', introApiUrl);
                }

                const response = await fetch(introApiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const htmlContent = await response.text();
                console.log('企业介绍HTML内容:', htmlContent);

                // 更新企业介绍内容
                const companyIntroElement = document.querySelector('#company-intro .expandable__body');
                if (companyIntroElement) {
                    companyIntroElement.innerHTML = htmlContent;

                    // 处理企业介绍中的图片URL和点击事件
                    const images = companyIntroElement.querySelectorAll('img');
                    images.forEach((img, index) => {
                        // 修复图片URL - 如果是相对路径，添加完整域名
                        let originalSrc = img.getAttribute('src');
                        let fixedSrc = originalSrc;

                        if (originalSrc.startsWith('/CustomerQuery/')) {
                            fixedSrc = `https://www.kunpeng360.com${originalSrc}`;
                        } else if (originalSrc.startsWith('CustomerQuery/')) {
                            fixedSrc = `https://www.kunpeng360.com/${originalSrc}`;
                        }

                        // 设置修复后的URL
                        img.src = fixedSrc;

                        // 添加图片加载错误处理
                        img.onerror = function() {
                            console.log('图片加载失败:', this.src);

                            if (!isProductionEnvironment()) {
                                // 开发环境：如果加载失败，尝试通过代理加载
                                const imageId = originalSrc.match(/imageId=([^&]+)/);
                                const t = originalSrc.match(/t=([^&]+)/);
                                if (imageId && t) {
                                    this.src = `http://localhost:3001/api/image?t=${t[1]}&imageId=${imageId[1]}`;
                                }
                            }
                            // 生产环境：图片加载失败就失败了，不再尝试代理
                        };

                        // 添加图片加载成功处理
                        img.onload = function() {
                            console.log('图片加载成功:', this.src);
                        };

                        img.style.cursor = 'pointer';
                        img.onclick = function() {
                            // 如果图片有onclick属性，执行原有的showPreview函数
                            if (this.hasAttribute('onclick')) {
                                const onclickValue = this.getAttribute('onclick');
                                if (onclickValue.includes('showPreview')) {
                                    // 提取showPreview的参数
                                    const match = onclickValue.match(/showPreview\('([^']+)',\s*true\)/);
                                    if (match) {
                                        const imageUrl = match[1];
                                        showImageModal(`https://www.kunpeng360.com${imageUrl}`, '企业介绍图片');
                                    }
                                }
                            } else {
                                // 否则使用图片的src
                                showImageModal(this.src, '企业介绍图片');
                            }
                        };
                    });
                }

                console.log('✅ 企业介绍已更新');

            } catch (error) {
                console.error('更新企业介绍时发生错误:', error);
                const companyIntroElement = document.querySelector('#company-intro .expandable__body');
                if (companyIntroElement) {
                    companyIntroElement.innerHTML = '<p>企业介绍加载失败</p>';
                }
            }
        }

        // 调用昆鹏360接口的函数
        async function callKunpengAPI() {
            let apiUrl;

            if (isProductionEnvironment()) {
                // 生产环境：从URL参数获取t和barcode
                const urlParams = new URLSearchParams(window.location.search);
                const t = urlParams.get('t') || '@t@';
                const barcode = urlParams.get('barcode') || '@barcode@';
                apiUrl = `https://www.kunpeng360.com/CustomerQuery/Query?t=${t}&barcode=${barcode}`;
                console.log('生产环境 - 直接调用昆鹏360 API:', apiUrl);
                console.log('参数 t:', t);
                console.log('参数 barcode:', barcode);
            } else {
                // 开发环境：使用代理服务器
                apiUrl = API_BASE_URL;
                console.log('开发环境 - 通过代理服务器调用API:', apiUrl);
            }

            try {

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log('代理服务器响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('=== API调用结果 ===');
                console.log('完整响应:', result);
                console.log('响应类型:', typeof result);
                console.log('是否为null:', result === null);
                console.log('是否为undefined:', result === undefined);

                // 检测环境并使用相应的数据结构
                let apiData;
                if (isProductionEnvironment()) {
                    // 生产环境：直接使用 result，检查是否有有效数据
                    console.log('=== 生产环境数据检查 ===');
                    console.log('result存在:', !!result);
                    if (result) {
                        console.log('result.EntName:', result.EntName);
                        console.log('result.EntCode:', result.EntCode);
                        console.log('result.Product:', result.Product);
                        console.log('result的所有键:', Object.keys(result));
                    }

                    if (result && (result.EntName || result.EntCode || result.Product)) {
                        apiData = result;
                        console.log('=== 生产环境 - 直接使用响应数据 ===');
                        console.log(apiData);
                    } else {
                        console.error('生产环境API返回数据无效:', result);
                        // 显示错误状态
                        showErrorState('API返回数据无效');
                        return result;
                    }
                } else {
                    // 开发环境：使用 result.data
                    if (result && result.success) {
                        apiData = result.data;
                        console.log('=== 开发环境 - 使用 result.data ===');
                        console.log('原始API返回数据:', result.rawData);
                        console.log('解析后的数据:', result.data);
                        console.log('API响应状态码:', result.statusCode);
                        console.log('API响应头:', result.headers);
                    } else {
                        console.error('开发环境API调用失败:', result ? result.error : '无响应数据');
                        // 显示错误状态
                        showErrorState('API调用失败');
                        return result;
                    }
                }

                // 🎯 关键：更新页面所有信息
                if (apiData) {
                    // 保存API数据到全局变量
                    globalApiData = apiData;

                    updatePageTitle(apiData);
                    updateCarousel(apiData);
                    updateProductInfo(apiData);
                    updateProductionTrace(apiData);
                    updateCompanyIntro(apiData);

                    // 初始化底部导航按钮功能
                    initializeBottomNavigation();

                    console.log('✅ 底部导航按钮功能已初始化');
                    console.log('集团官网URL:', apiData.OfficialWebsiteUrl);
                    console.log('企业简介URL:', apiData.EntPublicizeUrl);
                    console.log('联系客服URL:', apiData.ContactUsUrl);
                }

                return result;

            } catch (error) {
                if (isProductionEnvironment()) {
                    console.error('调用昆鹏360 API时发生错误:', error);
                    console.error('错误详情:', error.message);
                    console.log('请检查网络连接和API地址配置');
                } else {
                    console.error('调用代理服务器时发生错误:', error);
                    console.error('错误详情:', error.message);
                    console.log('请确保代理服务器正在运行 (node api-server.js)');
                }

                // 显示错误状态
                showErrorState('连接失败');

                return null;
            }
        }



        // 显示错误状态的函数
        function showErrorState(errorMessage) {
            // 更新产品信息容器
            const productInfoContainer = document.getElementById('product-info-container');
            if (productInfoContainer) {
                productInfoContainer.innerHTML = `<div class="product-info-loading">${errorMessage}</div>`;
            }

            // 更新生产追溯信息
            const traceElements = ['production-batch-no', 'production-date', 'expiration-date'];
            traceElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = errorMessage;
                }
            });
        }

        // 初始化底部导航按钮功能
        function initializeBottomNavigation() {
            // 集团官网按钮
            const officialWebsiteBtn = document.getElementById('official-website-btn');
            if (officialWebsiteBtn) {
                officialWebsiteBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (globalApiData && globalApiData.OfficialWebsiteUrl) {
                        window.open(globalApiData.OfficialWebsiteUrl, '_blank');
                        console.log('打开集团官网:', globalApiData.OfficialWebsiteUrl);
                    } else {
                        console.log('暂无集团官网链接');
                        alert('暂无集团官网链接');
                    }
                });
            }

            // 企业简介按钮
            const companyIntroBtn = document.getElementById('company-intro-btn');
            if (companyIntroBtn) {
                companyIntroBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (globalApiData && globalApiData.EntPublicizeUrl) {
                        window.open(globalApiData.EntPublicizeUrl, '_blank');
                        console.log('打开企业简介页面:', globalApiData.EntPublicizeUrl);
                    } else {
                        console.log('暂无企业简介链接');
                        alert('暂无');
                    }
                });
            }

            // 联系客服按钮
            const contactServiceBtn = document.getElementById('contact-service-btn');
            if (contactServiceBtn) {
                contactServiceBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (globalApiData && globalApiData.ContactUsUrl) {
                        window.open(globalApiData.ContactUsUrl, '_blank');
                        console.log('打开联系客服:', globalApiData.ContactUsUrl);
                    } else {
                        console.log('暂无联系客服链接');
                        alert('暂无联系客服链接');
                    }
                });
            }
        }

        // 显示图片弹窗
        function showImageModal(imageSrc, caption) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const imageCaption = document.getElementById('imageCaption');

            modal.style.display = 'block';
            modalImage.src = imageSrc;
            imageCaption.textContent = caption || '图片详情';

            // 添加淡入动画
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);
        }

        // 隐藏图片弹窗
        function hideImageModal() {
            const modal = document.getElementById('imageModal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 初始化图片弹窗事件
        function initImageModal() {
            const modal = document.getElementById('imageModal');
            const closeBtn = document.querySelector('.image-modal-close');

            // 点击关闭按钮
            closeBtn.onclick = hideImageModal;

            // 点击弹窗背景关闭
            modal.onclick = function(event) {
                if (event.target === modal) {
                    hideImageModal();
                }
            };

            // 按ESC键关闭
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && modal.style.display === 'block') {
                    hideImageModal();
                }
            });

            // 添加CSS过渡效果
            modal.style.transition = 'opacity 0.3s ease';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');

            // 初始化图片弹窗
            initImageModal();

            // 初始化折叠面板功能
            initExpandableItems();

            // 调试：检查折叠面板元素
            const headers = document.querySelectorAll('.expandable__header');
            console.log('🔍 找到折叠面板头部元素:', headers.length);
            headers.forEach((header, index) => {
                const target = header.getAttribute('data-target');
                const targetElement = document.getElementById(target);
                console.log(`📋 面板 ${index + 1}: target="${target}", 目标元素存在:`, !!targetElement);
            });

            // 初始化底部导航按钮功能（即使API失败也能工作）
            initializeBottomNavigation();

            // 调用真实API
            console.log('开始调用API...');
            callKunpengAPI().then(result => {
                let hasValidData = false;

                if (isProductionEnvironment()) {
                    // 生产环境：检查是否有EntName或EntCode
                    hasValidData = result && (result.EntName || result.EntCode);
                } else {
                    // 开发环境：检查result.success
                    hasValidData = result && result.success;
                }

                if (!hasValidData) {
                    console.error('API调用失败，无法获取数据');
                    console.log('返回的数据:', result);
                    // 显示错误状态
                    showErrorState('API调用失败');
                }
            }).catch(error => {
                console.error('API调用出错:', error);
                showErrorState('连接失败');
            });
        });

        // 初始化折叠面板功能（手风琴效果）
        function initExpandableItems() {
            const expandableHeaders = document.querySelectorAll('.expandable__header');

            expandableHeaders.forEach(header => {
                // 检查是否已经添加过事件监听器，避免重复添加
                if (!header.hasAttribute('data-initialized')) {
                    initExpandableItemForHeader(header);
                    header.setAttribute('data-initialized', 'true');
                }
            });

            console.log(`✅ 手风琴折叠面板功能已初始化，共 ${expandableHeaders.length} 个面板`);
        }

        // 为单个元素初始化折叠功能（手风琴效果）
        function initExpandableItemForElement(expandableItem) {
            const header = expandableItem.querySelector('.expandable__header');
            if (header && !header.hasAttribute('data-initialized')) {
                initExpandableItemForHeader(header);
                header.setAttribute('data-initialized', 'true');
                console.log(`✅ 为动态面板初始化手风琴功能: ${header.getAttribute('data-target')}`);
            }
        }

        // 为单个header初始化折叠功能（手风琴效果）
        function initExpandableItemForHeader(header) {
            header.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const targetContent = document.getElementById(targetId);
                const icon = this.querySelector('.expandable__icon');
                const item = this.closest('.expandable__item');

                if (targetContent && icon && item) {
                    const isActive = item.classList.contains('active');

                    // 🎯 手风琴效果：先关闭所有其他展开的项目
                    const allExpandableItems = document.querySelectorAll('.expandable__item');
                    const allExpandableHeaders = document.querySelectorAll('.expandable__header');

                    allExpandableHeaders.forEach(otherHeader => {
                        if (otherHeader !== this) {
                            const otherTargetId = otherHeader.getAttribute('data-target');
                            const otherTargetContent = document.getElementById(otherTargetId);
                            const otherIcon = otherHeader.querySelector('.expandable__icon');
                            const otherItem = otherHeader.closest('.expandable__item');

                            if (otherItem && otherIcon && otherTargetContent) {
                                // 折叠其他项目
                                otherItem.classList.remove('active');
                                otherIcon.classList.remove('bx-chevron-down');
                                otherIcon.classList.add('bx-chevron-right');
                                console.log(`🔄 手风琴效果：关闭其他面板 ${otherTargetId}`);
                            }
                        }
                    });

                    // 切换当前项目的展开/折叠状态
                    if (isActive) {
                        // 如果当前项目已展开，则折叠它
                        item.classList.remove('active');
                        icon.classList.remove('bx-chevron-down');
                        icon.classList.add('bx-chevron-right');
                        console.log(`📁 折叠面板: ${targetId}`);
                    } else {
                        // 如果当前项目未展开，则展开它
                        item.classList.add('active');
                        icon.classList.remove('bx-chevron-right');
                        icon.classList.add('bx-chevron-down');
                        console.log(`📂 展开面板: ${targetId}`);
                    }
                }
            });
        }

        // 也可以手动调用
        window.callKunpengAPI = callKunpengAPI;
        window.showImageModal = showImageModal;
        window.hideImageModal = hideImageModal;
    </script>
</body>

</html>